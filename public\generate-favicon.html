<!DOCTYPE html>
<html>
<head>
    <title>Generate Teelite Club Favicon</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas { 
            border: 2px solid #ddd; 
            margin: 10px; 
            background: white;
        }
        .preview {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .size-group {
            text-align: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .data-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
            max-height: 100px;
            overflow-y: auto;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Teelite Club Favicon Generator</h1>
        <p>Generate high-quality favicons with the letter "T" for Teelite Club</p>
        
        <div class="instructions">
            <h3>📋 How to use:</h3>
            <ol>
                <li>Click "Generate All Favicons" below</li>
                <li>Right-click each favicon image and select "Save image as..."</li>
                <li>Save with the exact filenames shown (e.g., favicon-16x16.png)</li>
                <li>Place all files in your public/ folder</li>
                <li>The favicon.ico should be saved as favicon-32x32.png and renamed to favicon.ico</li>
            </ol>
        </div>

        <button onclick="generateAllFavicons()">🚀 Generate All Favicons</button>
        <button onclick="downloadAll()">📥 Download All as ZIP</button>

        <div class="preview" id="preview"></div>
    </div>

    <script>
        function createFaviconCanvas(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#1e40af');    // Blue 800
            gradient.addColorStop(0.5, '#3b82f6');  // Blue 500
            gradient.addColorStop(1, '#1e3a8a');    // Blue 900
            
            // Draw background circle with slight padding
            const radius = (size / 2) - 1;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add subtle border
            ctx.strokeStyle = '#1e3a8a';
            ctx.lineWidth = size > 32 ? 2 : 1;
            ctx.stroke();
            
            // Draw letter T
            ctx.fillStyle = 'white';
            
            // Calculate font size and positioning
            const fontSize = Math.floor(size * 0.65);
            ctx.font = `bold ${fontSize}px Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // Add text shadow for better visibility
            ctx.shadowColor = 'rgba(0,0,0,0.3)';
            ctx.shadowBlur = size > 32 ? 2 : 1;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 1;
            
            ctx.fillText('T', size/2, size/2);
            
            return canvas;
        }

        function generateAllFavicons() {
            const sizes = [
                { size: 16, filename: 'favicon-16x16.png', description: '16×16 (Browser tab)' },
                { size: 32, filename: 'favicon-32x32.png', description: '32×32 (Main favicon)' },
                { size: 48, filename: 'favicon-48x48.png', description: '48×48 (Windows)' },
                { size: 180, filename: 'apple-touch-icon.png', description: '180×180 (Apple touch icon)' }
            ];

            const preview = document.getElementById('preview');
            preview.innerHTML = '';

            sizes.forEach(({ size, filename, description }) => {
                const canvas = createFaviconCanvas(size);
                
                const container = document.createElement('div');
                container.className = 'size-group';
                
                container.innerHTML = `
                    <h4>${filename}</h4>
                    <p>${description}</p>
                `;
                
                container.appendChild(canvas);
                
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = `Download ${size}×${size}`;
                downloadBtn.onclick = () => downloadCanvas(canvas, filename);
                container.appendChild(downloadBtn);
                
                // Add data URL for manual saving
                const dataUrl = canvas.toDataURL('image/png');
                const dataUrlDiv = document.createElement('div');
                dataUrlDiv.className = 'data-url';
                dataUrlDiv.innerHTML = `<small>Data URL (for manual creation):</small><br>${dataUrl.substring(0, 100)}...`;
                container.appendChild(dataUrlDiv);
                
                preview.appendChild(container);
            });

            // Add special note for favicon.ico
            const icoNote = document.createElement('div');
            icoNote.innerHTML = `
                <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0;">
                    <h4>📝 Important Note:</h4>
                    <p>For <strong>favicon.ico</strong>, download the 32×32 version and rename it to <code>favicon.ico</code></p>
                    <p>Modern browsers will automatically handle the PNG format even with .ico extension.</p>
                </div>
            `;
            preview.appendChild(icoNote);
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function downloadAll() {
            // Generate and download all favicons with a small delay between each
            const sizes = [16, 32, 48, 180];
            const filenames = ['favicon-16x16.png', 'favicon-32x32.png', 'favicon-48x48.png', 'apple-touch-icon.png'];
            
            sizes.forEach((size, index) => {
                setTimeout(() => {
                    const canvas = createFaviconCanvas(size);
                    downloadCanvas(canvas, filenames[index]);
                }, index * 500); // 500ms delay between downloads
            });
        }

        // Auto-generate on page load
        window.addEventListener('load', generateAllFavicons);
    </script>
</body>
</html>
