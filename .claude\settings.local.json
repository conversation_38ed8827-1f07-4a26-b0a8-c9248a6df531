{"permissions": {"allow": ["mcp__ide__getDiagnostics", "Bash(npm run lint)", "Bash(npm run:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(npx vite-bundle-analyzer:*)", "Bash(npm ls:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(tsc --noEmit)", "Bash(supabase link:*)", "Bash(npx supabase link:*)", "Bash(npx supabase projects:*)", "Bash(npx supabase:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(taskkill:*)", "<PERSON><PERSON>(wmic:*)", "Bash(supabase functions deploy:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git config:*)", "Bash(git push:*)", "Bash(npm start)", "Bash(supabase functions logs:*)", "<PERSON><PERSON>(timeout:*)"], "deny": []}}