<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with modern gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="1" stdDeviation="1" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#bgGradient)" stroke="#1e3a8a" stroke-width="1" />

  <!-- Letter T with better positioning and styling -->
  <g filter="url(#shadow)">
    <path d="M 8 9 L 24 9 L 24 12 L 18 12 L 18 23 L 14 23 L 14 12 L 8 12 Z" fill="white" />
  </g>
</svg>
