project_id = "ngucthauvvjajdjcdrvl"

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[api.tls]
enabled = false

[db]
port = 54322
shadow_port = 54320
major_version = 15

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = true
ip_version = "IPv6"

[studio]
enabled = true
port = 54323
api_url = "http://127.0.0.1:54321"

[inbucket]
enabled = true
port = 54324
pop3_port = 54325
smtp_port = 54326

[storage]
enabled = true
file_size_limit = "50MiB"

[storage.image_transformation]
enabled = true

[auth]
enabled = true
site_url = "http://localhost:8080"
additional_redirect_urls = ["https://60d72789-0683-4067-bb6d-e123ec901c57.lovableproject.com/**", "https://clothly-commerce-hub.lovable.app/**", "https://id-preview--60d72789-0683-4067-bb6d-e123ec901c57.lovable.app/**", "https://preview--clothly-commerce-hub.lovable.app/**", "http://localhost:8080", "http://localhost:8081", "http://localhost:3000"]
jwt_expiry = 3600
enable_refresh_token_rotation = true
refresh_token_reuse_interval = 10
enable_manual_linking = false

[auth.external.apple]
enabled = false
client_id = ""
secret = ""

[auth.external.azure]
enabled = false
client_id = ""
secret = ""
url = ""

[auth.external.bitbucket]
enabled = false
client_id = ""
secret = ""

[auth.external.discord]
enabled = false
client_id = ""
secret = ""

[auth.external.facebook]
enabled = false
client_id = ""
secret = ""

[auth.external.figma]
enabled = false
client_id = ""
secret = ""

[auth.external.github]
enabled = false
client_id = ""
secret = ""

[auth.external.gitlab]
enabled = false
client_id = ""
secret = ""
url = ""

[auth.external.google]
enabled = false
client_id = ""
secret = ""
skip_nonce_check = false

[auth.external.keycloak]
enabled = false
client_id = ""
secret = ""
url = ""

[auth.external.linkedin_oidc]
enabled = false
client_id = ""
secret = ""

[auth.external.notion]
enabled = false
client_id = ""
secret = ""

[auth.external.twitch]
enabled = false
client_id = ""
secret = ""

[auth.external.twitter]
enabled = false
client_id = ""
secret = ""

[auth.external.slack]
enabled = false
client_id = ""
secret = ""

[auth.external.spotify]
enabled = false
client_id = ""
secret = ""

[auth.external.workos]
enabled = false
client_id = ""
secret = ""
url = ""

[auth.external.zoom]
enabled = false
client_id = ""
secret = ""

[auth.mfa]
max_enrolled_factors = 10

[auth.mfa.totp]
enroll_enabled = true
verify_enabled = true

[auth.mfa.phone]
enroll_enabled = false
verify_enabled = false

[auth.mfa.web_authn]
enroll_enabled = true
verify_enabled = true

[auth.sessions]
timebox = "24h0m0s"
inactivity_timeout = "8h0m0s"

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false
secure_password_change = false
max_frequency = "1m0s"

[auth.sms]
enable_signup = false
enable_confirmations = false
max_frequency = "1s"

[edge_runtime]
enabled = true
inspector_port = 8083

[functions.create-midtrans-payment]
verify_jwt = false

[functions.midtrans-webhook]
verify_jwt = false

[functions.check-payment-status]
verify_jwt = false