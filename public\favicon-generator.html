<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon Generator - Teelite Club</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .favicon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .favicon-size {
            text-align: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .instructions {
            background: #e0f2fe;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Teelite Club Favicon Generator</h1>
        <p>This tool generates favicons with the letter "T" for the Teelite Club e-commerce site.</p>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Generate Favicons" to create the favicon images</li>
                <li>Right-click on each favicon and "Save image as..." to download</li>
                <li>Save them in the public folder with the correct names</li>
                <li>The favicons will have a blue gradient background with white "T"</li>
            </ol>
        </div>

        <button onclick="generateFavicons()">Generate Favicons</button>
        
        <div class="favicon-preview" id="faviconPreview">
            <!-- Favicons will be generated here -->
        </div>
    </div>

    <script>
        function generateFavicons() {
            const sizes = [
                { size: 16, name: 'favicon-16x16.png' },
                { size: 32, name: 'favicon-32x32.png' },
                { size: 48, name: 'favicon-48x48.png' },
                { size: 180, name: 'apple-touch-icon.png' }
            ];

            const preview = document.getElementById('faviconPreview');
            preview.innerHTML = '';

            sizes.forEach(({ size, name }) => {
                const container = document.createElement('div');
                container.className = 'favicon-size';
                
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                
                const ctx = canvas.getContext('2d');
                
                // Create gradient background
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#1e40af');
                gradient.addColorStop(0.5, '#3b82f6');
                gradient.addColorStop(1, '#1e3a8a');
                
                // Draw background circle
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
                ctx.fill();
                
                // Draw border
                ctx.strokeStyle = '#1e3a8a';
                ctx.lineWidth = 1;
                ctx.stroke();
                
                // Draw letter T
                ctx.fillStyle = 'white';
                ctx.font = `bold ${size * 0.6}px Arial, sans-serif`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('T', size/2, size/2);
                
                container.innerHTML = `
                    <h4>${name}</h4>
                    <div>${size}x${size}px</div>
                `;
                container.appendChild(canvas);
                
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = 'Download';
                downloadBtn.onclick = () => downloadCanvas(canvas, name);
                container.appendChild(downloadBtn);
                
                preview.appendChild(container);
            });
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        // Generate favicons on page load
        window.onload = generateFavicons;
    </script>
</body>
</html>
